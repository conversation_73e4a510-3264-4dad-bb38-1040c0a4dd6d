package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;


@Slf4j
public class MarketPriceEnrichedConsumer extends AbstractMessageConsumer<Long, MarketPriceEnrichedEvent> {

    private final MarketPriceDataService marketPriceDataService;
    private final ExecutorService executorService;

    public MarketPriceEnrichedConsumer(KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> kafkaConsumerEndpoint,
                                      MarketPriceDataService marketPriceDataService,
                                      @Qualifier("marketPriceExecutor") ExecutorService executorService) {
        super(kafkaConsumerEndpoint);
        this.marketPriceDataService = marketPriceDataService;
        this.executorService = executorService;
    }
    @Override
    protected void processMessages(Collection<MarketPriceEnrichedEvent> messages, String pollId) {
        log.info("MarketPriceEnrichedConsumer Processing {} messages with pollId: {}", messages.size(), pollId);

        long startTime = System.currentTimeMillis();
        marketPriceDataService.setRedisKey("ts:MarketPrice:");

        List<MarketPriceDetail> allDetails = new ArrayList<>();
        for (MarketPriceEnrichedEvent message : messages) {
            if (message.getMarketPriceDataList() != null && !message.getMarketPriceDataList().isEmpty()) {
                allDetails.addAll(message.getMarketPriceDataList());
            }
        }

        if (allDetails.isEmpty()) {
            log.info("MarketPriceEnrichedConsumer: No market price details found in messages for pollId: {}", pollId);
            return;
        }

        List<Future<?>> futures = new ArrayList<>();
        for (MarketPriceDetail detail : allDetails) {
            Future<?> future = executorService.submit(() -> {
                try {
                    marketPriceDataService.storeMarketPriceDetail(detail);
                } catch (Exception e) {
                    log.error("MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: {}, date: {}",
                             detail.getPerformanceId(), detail.getDate(), e);
                }
            });
            futures.add(future);
        }

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("MarketPriceEnrichedConsumer: Error waiting for task completion in pollId: {}", pollId, e);
            }
        }

        long processingTime = System.currentTimeMillis() - startTime;
        log.info("MarketPriceEnrichedConsumer: Completed processing {} details with pollId: {} in {}ms",
                allDetails.size(), pollId, processingTime);
    }
}

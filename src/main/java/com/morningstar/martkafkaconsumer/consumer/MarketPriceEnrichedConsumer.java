package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;


@Slf4j
public class MarketPriceEnrichedConsumer extends AbstractMessageConsumer<Long, MarketPriceEnrichedEvent> {

    private final MarketPriceDataService marketPriceDataService;

    public MarketPriceEnrichedConsumer(KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> kafkaConsumerEndpoint,
                                      MarketPriceDataService marketPriceDataService) {
        super(kafkaConsumerEndpoint);
        this.marketPriceDataService = marketPriceDataService;
    }
    @Override
    protected void processMessages(Collection<MarketPriceEnrichedEvent> messages, String pollId) {
        log.info("MarketPriceEnrichedConsumer Processing {} messages with pollId: {}", messages.size(), pollId);

        for(MarketPriceEnrichedEvent message : messages) {

            if (message.getMarketPriceDataList() == null || message.getMarketPriceDataList().isEmpty()) {
                log.warn("MarketPriceEnrichedConsumer: No market price data found in message, skipping");
                continue;
            }

            for (MarketPriceDetail marketPriceDetail : message.getMarketPriceDataList()) {
                try {

                    marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);

                } catch (Exception e) {
                    log.error("MarketPriceEnrichedConsumer: Failed to process MarketPriceDetail for performanceId: {}, date: {}",
                             marketPriceDetail.getPerformanceId(), marketPriceDetail.getDate(), e);
                }
            }
        }

        log.info("MarketPriceEnrichedConsumer: Completed processing {} messages with pollId: {}", messages.size(), pollId);
    }
}

package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.martkafkaconsumer.service.AsyncMarketPriceProcessingService;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class MarketPriceRawConsumer extends  AbstractMessageConsumer<Long, MarketPriceEnrichedEvent>{
    private final MarketPriceDataService marketPriceDataService;
    private final AsyncMarketPriceProcessingService asyncProcessingService;
    private static final String REDIS_KEY_PREFIX = "ts:RawMarketPrice:";

    public MarketPriceRawConsumer(KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> kafkaConsumerEndpoint,
                                  MarketPriceDataService marketPriceDataService,
                                  AsyncMarketPriceProcessingService asyncProcessingService) {
        super(kafkaConsumerEndpoint);
        this.marketPriceDataService = marketPriceDataService;
        this.asyncProcessingService = asyncProcessingService;
    }
    @Override
    protected void processMessages(Collection<MarketPriceEnrichedEvent> messages, String pollId) {
        log.info("MarketPriceRawConsumer Processing {} messages with pollId: {}", messages.size(), pollId);

        long startTime = System.currentTimeMillis();

        try {
            if (messages.size() > 10) {
                CompletableFuture<Void> asyncProcessing = asyncProcessingService.processMessagesAsync(
                    messages, pollId, REDIS_KEY_PREFIX);
                asyncProcessing.join();
            } else {
                processSynchronously(messages, pollId);
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("MarketPriceRawConsumer: Completed processing {} messages with pollId: {} in {}ms",
                    messages.size(), pollId, processingTime);

        } catch (Exception e) {
            log.error("MarketPriceRawConsumer: Error processing messages with pollId: {}", pollId, e);
            throw e;
        }
    }

    private void processSynchronously(Collection<MarketPriceEnrichedEvent> messages, String pollId) {
        marketPriceDataService.setRedisKey(REDIS_KEY_PREFIX);

        for(MarketPriceEnrichedEvent message : messages) {
            if (message.getMarketPriceDataList() == null || message.getMarketPriceDataList().isEmpty()) {
                log.warn("MarketPriceRawConsumer: No market price data found in message, skipping");
                continue;
            }

            for (MarketPriceDetail marketPriceDetail : message.getMarketPriceDataList()) {
                try {
                    marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
                } catch (Exception e) {
                    log.error("MarketPriceRawConsumer: Failed to process MarketPriceDetail for performanceId: {}, date: {}",
                            marketPriceDetail.getPerformanceId(), marketPriceDetail.getDate(), e);
                }
            }
        }
    }
}

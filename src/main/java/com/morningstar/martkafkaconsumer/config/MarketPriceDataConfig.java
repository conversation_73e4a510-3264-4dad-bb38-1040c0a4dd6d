package com.morningstar.martkafkaconsumer.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;


@Data
@ConfigurationProperties(prefix = "market-price")
public class MarketPriceDataConfig {

    private String redisKeyPrefix = "ts:MarketPrice:";

    private int yearsPerKey = 10;

    private int yearsPerField = 2;

    private long defaultExpirationHours = 24;

    private Map<String, String> dataPointMapping = Map.of(
        "high", "EO001",
        "low", "EO002", 
        "open", "EO003",
        "close", "EO004",
        "tradedVolume", "EO005",
        "copyOverReason", "EO006"
    );

}

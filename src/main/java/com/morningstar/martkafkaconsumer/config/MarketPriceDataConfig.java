package com.morningstar.martkafkaconsumer.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * Configuration properties for MarketPrice data storage
 */
@Data
@ConfigurationProperties(prefix = "market-price")
public class MarketPriceDataConfig {

    // Getters and setters
    /**
     * Redis key prefix for market price data
     */
    private String redisKeyPrefix = "ts:MarketPrice:";
    
    /**
     * Number of years per Redis key (default: 10 years)
     */
    private int yearsPerKey = 10;
    
    /**
     * Number of years per field (default: 2 years)
     */
    private int yearsPerField = 2;
    
    /**
     * Default expiration hours for Redis data
     */
    private long defaultExpirationHours = 24;
    
    /**
     * Data point mappings from field names to data point IDs
     */
    private Map<String, String> dataPointMapping = Map.of(
        "high", "EO001",
        "low", "EO002", 
        "open", "EO003",
        "close", "EO004",
        "tradedVolume", "EO005",
        "copyOverReason", "EO006"
    );

}

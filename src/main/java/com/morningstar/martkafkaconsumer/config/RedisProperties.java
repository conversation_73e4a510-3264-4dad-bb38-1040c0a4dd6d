package com.morningstar.martkafkaconsumer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@ConfigurationProperties(prefix = "redis")
public class RedisProperties {

    private TsData tsData = new TsData();
    private String clientName;

    @Data
    public static class TsData {
        private String host;
        private int port;
    }
}

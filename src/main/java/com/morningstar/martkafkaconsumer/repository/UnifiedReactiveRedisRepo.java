package com.morningstar.martkafkaconsumer.repository;

import com.morningstar.martkafkaconsumer.util.NumberUtil;
import com.morningstar.martkafkaconsumer.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
public class UnifiedReactiveRedisRepo {

    private final List<ReactiveRedisTemplate<byte[], byte[]>> templateList;
    private final List<LettuceConnectionFactory> connectionFactories;
    private final ThreadLocal<Integer> threadIndex = ThreadLocal.withInitial(() -> -1);

    // Main constructor - accepts factory list, each factory corresponds to an independent connection
    public UnifiedReactiveRedisRepo(List<LettuceConnectionFactory> factories) {
        if (CollectionUtils.isEmpty(factories)) {
            throw new IllegalArgumentException("Factory list cannot be null or empty");
        }

        this.connectionFactories = factories; // Store factories reference for cleanup
        templateList = createRedisTemplates(factories);
    }

    /**
     * Create ReactiveRedisTemplate instances from LettuceConnectionFactory list
     * This method can be overridden in tests for mocking purposes
     *
     * @param factories List of LettuceConnectionFactory instances
     * @return List of ReactiveRedisTemplate instances
     */
    protected List<ReactiveRedisTemplate<byte[], byte[]>> createRedisTemplates(List<LettuceConnectionFactory> factories) {
        List<ReactiveRedisTemplate<byte[], byte[]>> templates = new ArrayList<>();
        factories.forEach(factory -> {
            ReactiveRedisTemplate<byte[], byte[]> template = new ReactiveRedisTemplate<>(factory, RedisSerializationContext.byteArray());
            templates.add(template);
        });
        return templates;
    }

    private int getCurrentIndex() {
        Integer index = threadIndex.get();
        if (index == -1) {
            index = ThreadLocalRandom.current().nextInt(templateList.size());
            threadIndex.set(index);
        }
        return index;
    }

    /**
     * Get hash values from Redis as an ordered list
     *
     * @param redisKey The Redis key as byte array
     * @param hashFields List of hash fields to retrieve (expire field will be automatically added)
     * @return Mono containing a List of byte arrays in the same order as hashFields
     *
     * @apiNote This method preserves the order of values corresponding to the hashFields order.
     *          The returned list may contain null values where Redis fields don't exist or are null.
     *          Use this method when you need positional correspondence between fields and values.
     *
     * @implNote The expire field is automatically added to hashFields and removed from the result.
     *           Returns an empty list if cache is expired or no data is found.
     */
    public Mono<List<byte[]>> getHashValueList(byte[] redisKey, List<byte[]> hashFields) {
        return getHashValueList(redisKey, hashFields, false);
    }

    /**
     * Get hash values from Redis as an ordered list with expire field handling control
     *
     * @param redisKey The Redis key as byte array
     * @param hashFields List of hash fields to retrieve
     * @param alreadyAddedExpireField If true, assumes expire field is already included and won't add/remove it
     * @return Mono containing a List of byte arrays in the same order as hashFields
     *
     * @apiNote This method preserves the order of values corresponding to the hashFields order.
     *          The returned list may contain null values where Redis fields don't exist or are null.
     *          Use this method when you need positional correspondence between fields and values.
     *
     * @implNote When alreadyAddedExpireField is false, the expire field is automatically added to hashFields and removed from the result.
     *           When alreadyAddedExpireField is true, caller is responsible for expire field management.
     *           Returns an empty list if cache is expired or no data is found.
     */
    private Mono<List<byte[]>> getHashValueList(byte[] redisKey, List<byte[]> hashFields, boolean alreadyAddedExpireField) {
        List<byte[]> fieldsToQuery;

        if (alreadyAddedExpireField) {
            fieldsToQuery = hashFields;
        } else {
            // Create a shallow copy to avoid modifying the caller's original list
            fieldsToQuery = new ArrayList<>(hashFields);
            fieldsToQuery.add(RedisUtil.getExpireKey());
        }

        int index = getCurrentIndex();
        return templateList.get(index).<byte[], byte[]>opsForHash().multiGet(redisKey, fieldsToQuery)
                .flatMap(list -> {
                    if (CollectionUtils.isEmpty(list) || isCacheExpired(list.get(list.size() - 1), redisKey)) {
                        return Mono.empty();
                    }

                    // Remove the expiry field before returning
                    return Mono.just(list.subList(0, list.size() - 1));
                });
    }

    public Flux<byte[]> getHashValue(byte[] redisKey, List<byte[]> hashFields) {
        return getHashValueList(redisKey, hashFields)
                .flatMapMany(list -> Flux.fromStream(list.stream().filter(o -> o != null && o.length > 0)));
    }

    /**
     * Get multiple hash values from multiple keys
     * @param cacheKeys List of cache keys as byte arrays
     * @param hashFields List of hash fields as byte arrays (expire field will be added automatically)
     * @return Flux of maps containing the hash data (key -> field -> value)
     */
    public Flux<Map<byte[], byte[]>> multiGetHashValues(List<byte[]> cacheKeys, List<byte[]> hashFields) {
        // Create a shallow copy and add expire field once
        List<byte[]> fieldsWithExpire = new ArrayList<>(hashFields);
        fieldsWithExpire.add(RedisUtil.getExpireKey());

        return Flux.fromIterable(cacheKeys)
                .flatMap(cacheKey -> getHashValueList(cacheKey, fieldsWithExpire, true) // Pass true since we already added expire
                        .flatMapMany(values -> buildRowDataMap(values, fieldsWithExpire)));
    }

    /**
     * Build row data map from values and fields
     * @param values List of byte arrays values
     * @param fieldsWithExpire List of fields including expire field
     * @return Mono of the row data map or empty if no valid data
     */
    private Mono<Map<byte[], byte[]>> buildRowDataMap(List<byte[]> values, List<byte[]> fieldsWithExpire) {
        if (CollectionUtils.isEmpty(values)) {
            return Mono.empty();
        }

        Map<byte[], byte[]> rowData = new HashMap<>();
        for (int i = 0; i < values.size(); i++) {
            if (ArrayUtils.isNotEmpty(values.get(i))) {
                rowData.put(fieldsWithExpire.get(i), values.get(i));
            }
        }
        return MapUtils.isEmpty(rowData) ? Mono.empty() : Mono.just(rowData);
    }

    private boolean isCacheExpired(byte[] expireBytes, byte[] redisKey) {
        // No expire field, consider cache as valid (not expired)
        if (expireBytes == null) {
            return false;
        }

        long expireTimeMillis = NumberUtil.bytesToLong(expireBytes, 0);
        // Invalid expire field format, treat as expired
        if (expireTimeMillis == 0) {
            log.info("RedisTsRepo: Invalid expire format for key: {}, treating as expired", new String(redisKey));
            return true;
        }
        return expireTimeMillis < System.currentTimeMillis();
    }

    public Flux<Object> multiSetHash(Map<byte[], Map<byte[], byte[]>> data, long expireHours) {
        return Flux.fromIterable(data.entrySet()).flatMap(e -> setHash(e.getKey(), e.getValue(), expireHours));
    }

    public Flux<Boolean> setHash(byte[] key, Map<byte[], byte[]> fieldMap, long expireHours) {
        if (MapUtils.isNotEmpty(fieldMap)) {
            // Create a copy to avoid mutating the input map
            Map<byte[], byte[]> fieldMapCopy = new HashMap<>(fieldMap);
            RedisUtil.addExpireField(fieldMapCopy, expireHours, RedisUtil.TimeUnit.HOURS);

            int index = getCurrentIndex();
            ReactiveRedisTemplate<byte[], byte[]> template = templateList.get(index);

            return template.<byte[], byte[]>opsForHash().putAll(key, fieldMapCopy)
                    .flatMap(success -> {
                        if (success) {
                            return template.expire(key, Duration.ofHours(expireHours))
                                    .map(expireSuccess -> true);
                        } else {
                            return Mono.empty();
                        }
                    })
                    .flux()
                    .onErrorResume(e -> {
                        log.error("UnifiedRedisRepo: setHash error for key: {} on Redis instance {}", new String(key), index, e);
                        return Flux.empty();
                    })
                    .retryWhen(Retry.max(2)
                            .doBeforeRetry(retrySignal -> log.info("UnifiedRedisRepo: setHash exception retry {} times.", retrySignal.totalRetries() + 1))
                    );
        } else {
            log.info("UnifiedRedisRepo: setHash data is empty. key:{}", new String(key));
            return Flux.empty();
        }
    }

    /**
     * Set hash data without expiration
     * @param key Cache key
     * @param fieldMap Hash field-value pairs
     * @return Flux<Boolean> indicating success
     */
    public Flux<Boolean> setHashWithoutExpiration(byte[] key, Map<byte[], byte[]> fieldMap) {
        if (MapUtils.isNotEmpty(fieldMap)) {
            int index = getCurrentIndex();
            ReactiveRedisTemplate<byte[], byte[]> template = templateList.get(index);

            return template.<byte[], byte[]>opsForHash().putAll(key, fieldMap)
                    .flux()
                    .onErrorResume(e -> {
                        log.error("UnifiedRedisRepo: setHashWithoutExpiration error for key: {} on Redis instance {}",
                                new String(key), index, e);
                        return Flux.empty();
                    })
                    .retryWhen(Retry.max(2)
                            .doBeforeRetry(retrySignal ->
                                log.info("UnifiedRedisRepo: setHashWithoutExpiration exception retry {} times.",
                                        retrySignal.totalRetries() + 1))
                    );
        } else {
            log.info("UnifiedRedisRepo: setHashWithoutExpiration data is empty. key:{}", new String(key));
            return Flux.empty();
        }
    }

    /**
     * Get string value from Redis
     * @param key Cache key
     * @return Mono containing the string value
     */
    public Mono<String> getString(String key) {
        if (StringUtils.isEmpty(key)) {
            return Mono.empty();
        }

        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        int index = getCurrentIndex();
        return templateList.get(index).opsForValue().get(keyBytes)
                .map(bytes -> new String(bytes, StandardCharsets.UTF_8));
    }

    /**
     * Set string value with expiration
     * @param key Cache key
     * @param value String value to set
     * @param ttl Time to live duration
     * @return Mono<Boolean> indicating success
     */
    public Mono<Boolean> setString(String key, String value, Duration ttl) {
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        byte[] valueBytes = value.getBytes(StandardCharsets.UTF_8);
        int index = getCurrentIndex();
        return templateList.get(index).opsForValue().set(keyBytes, valueBytes, ttl);
    }

    /**
     * Cleanup method to properly close all connection factories and their resources
     * Should be called when the repository is no longer needed or during application shutdown
     */
    public void destroy() {
        connectionFactories.forEach(factory -> {
            try {
                if (factory != null) {
                    factory.destroy();
                }
            } catch (Exception e) {
                log.warn("Error closing connection factory: {}", e.getMessage(), e);
            }
        });
    }
}

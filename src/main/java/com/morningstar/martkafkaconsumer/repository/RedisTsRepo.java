package com.morningstar.martkafkaconsumer.repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import reactor.core.publisher.Flux;
import javax.annotation.PreDestroy;

@Slf4j
public class RedisTsRepo {
    private final UnifiedReactiveRedisRepo unifiedReactiveRedisRepo;

    // Constructor accepting List of LettuceConnectionFactory for better performance
    public RedisTsRepo(List<LettuceConnectionFactory> factories) {
        this.unifiedReactiveRedisRepo = new UnifiedReactiveRedisRepo(factories);
    }

    /**
     * Get hash values for given fields from Redis
     * @param redisKey Redis key
     * @param hashFields List of hash fields to retrieve
     * @return Flux of byte arrays containing the values
     */
    public Flux<byte[]> getHashValue(byte[] redisKey, List<byte[]> hashFields) {
        return unifiedReactiveRedisRepo.getHashValue(redisKey, hashFields);
    }

    public Map<byte[], byte[]> getMultipleHashValues(byte[] redisKey, List<byte[]> hashFields) {
        return unifiedReactiveRedisRepo.getHashValueList(redisKey, hashFields)
                .map(valueList -> {
                    Map<byte[], byte[]> resultMap = new HashMap<>();
                    for (int i = 0; i < hashFields.size() && i < valueList.size(); i++) {
                        byte[] value = valueList.get(i);
                        if (value != null && value.length > 0) {
                            resultMap.put(hashFields.get(i), value);
                        }
                    }
                    return resultMap;
                })
                .blockOptional()
                .orElse(new HashMap<>());
    }

    public Flux<Object> multiSetHash(Map<byte[], Map<byte[], byte[]>> data, long expireHours) {
        return unifiedReactiveRedisRepo.multiSetHash(data, expireHours);
    }

    /**
     * Set hash data for a single key with expiration
     * @param key Redis key
     * @param data Hash field-value pairs
     * @param expireHours Expiration time in hours
     * @return Flux of operation result
     */
    public Flux<Boolean> setHash(byte[] key, Map<byte[], byte[]> data, long expireHours) {
        return unifiedReactiveRedisRepo.setHash(key, data, expireHours);
    }

    /**
     * Set hash data for a single key without expiration
     * @param key Redis key
     * @param data Hash field-value pairs
     * @return Flux of operation result
     */
    public Flux<Boolean> setHashWithoutExpiration(byte[] key, Map<byte[], byte[]> data) {
        return unifiedReactiveRedisRepo.setHashWithoutExpiration(key, data);
    }

    /**
     * Cleanup method called when the bean is destroyed
     * Delegates to UnifiedRedisRepo for proper resource cleanup
     */
    @PreDestroy
    public void destroy() {
        unifiedReactiveRedisRepo.destroy();
    }
}
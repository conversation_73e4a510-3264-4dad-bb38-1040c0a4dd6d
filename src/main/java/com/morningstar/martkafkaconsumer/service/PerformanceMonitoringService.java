package com.morningstar.martkafkaconsumer.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Service
public class PerformanceMonitoringService {

    private final ConcurrentHashMap<String, AtomicLong> processingTimes = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> messagesCounts = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> errorCounts = new ConcurrentHashMap<>();

    public void recordProcessingTime(String consumerType, long processingTimeMs) {
        processingTimes.computeIfAbsent(consumerType, k -> new AtomicLong(0))
                      .addAndGet(processingTimeMs);
    }

    public void recordMessageCount(String consumerType, int messageCount) {
        messagesCounts.computeIfAbsent(consumerType, k -> new AtomicLong(0))
                     .addAndGet(messageCount);
    }

    public void recordError(String consumerType) {
        errorCounts.computeIfAbsent(consumerType, k -> new AtomicLong(0))
                  .incrementAndGet();
    }

    public void logPerformanceStats() {
        log.info("=== Performance Statistics ===");
        
        processingTimes.forEach((consumerType, totalTime) -> {
            long messageCount = messagesCounts.getOrDefault(consumerType, new AtomicLong(0)).get();
            long errorCount = errorCounts.getOrDefault(consumerType, new AtomicLong(0)).get();
            
            if (messageCount > 0) {
                double avgProcessingTime = (double) totalTime.get() / messageCount;
                log.info("{}: Total Messages: {}, Total Time: {}ms, Avg Time: {:.2f}ms, Errors: {}", 
                        consumerType, messageCount, totalTime.get(), avgProcessingTime, errorCount);
            }
        });
        
        log.info("===============================");
    }

    public void resetStats() {
        processingTimes.clear();
        messagesCounts.clear();
        errorCounts.clear();
        log.info("Performance statistics reset");
    }
}

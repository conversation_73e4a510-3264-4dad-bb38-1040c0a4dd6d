package com.morningstar.martkafkaconsumer.service;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AsyncMarketPriceProcessingService {

    private final MarketPriceDataService marketPriceDataService;
    private final Executor batchProcessingTaskExecutor;
    private static final int BATCH_SIZE = 50;

    @Autowired
    public AsyncMarketPriceProcessingService(MarketPriceDataService marketPriceDataService,
                                           @Qualifier("batchProcessingTaskExecutor") Executor batchProcessingTaskExecutor) {
        this.marketPriceDataService = marketPriceDataService;
        this.batchProcessingTaskExecutor = batchProcessingTaskExecutor;
    }

    @Async("marketPriceTaskExecutor")
    public CompletableFuture<Void> processMessagesAsync(Collection<MarketPriceEnrichedEvent> messages, 
                                                        String pollId, String redisKeyPrefix) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.debug("AsyncProcessing: Starting async processing of {} messages with pollId: {}", 
                         messages.size(), pollId);
                
                marketPriceDataService.setRedisKey(redisKeyPrefix);
                
                List<MarketPriceDetail> allDetails = extractAllMarketPriceDetails(messages);
                
                if (allDetails.isEmpty()) {
                    log.debug("AsyncProcessing: No market price details found in messages for pollId: {}", pollId);
                    return;
                }

                List<CompletableFuture<Void>> batchFutures = processBatches(allDetails, pollId);
                
                CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0])).join();
                
                log.debug("AsyncProcessing: Completed async processing of {} details with pollId: {}", 
                         allDetails.size(), pollId);
                
            } catch (Exception e) {
                log.error("AsyncProcessing: Error in async processing for pollId: {}", pollId, e);
                throw new RuntimeException("Async processing failed", e);
            }
        }, batchProcessingTaskExecutor);
    }

    private List<MarketPriceDetail> extractAllMarketPriceDetails(Collection<MarketPriceEnrichedEvent> messages) {
        List<MarketPriceDetail> allDetails = new ArrayList<>();
        
        for (MarketPriceEnrichedEvent message : messages) {
            if (message.getMarketPriceDataList() != null && !message.getMarketPriceDataList().isEmpty()) {
                allDetails.addAll(message.getMarketPriceDataList());
            }
        }
        
        return allDetails;
    }

    private List<CompletableFuture<Void>> processBatches(List<MarketPriceDetail> allDetails, String pollId) {
        List<List<MarketPriceDetail>> batches = partitionList(allDetails, BATCH_SIZE);
        
        return batches.stream()
                .map(batch -> processBatchAsync(batch, pollId))
                .collect(Collectors.toList());
    }

    @Async("batchProcessingTaskExecutor")
    public CompletableFuture<Void> processBatchAsync(List<MarketPriceDetail> batch, String pollId) {
        return CompletableFuture.runAsync(() -> {
            String threadName = Thread.currentThread().getName();
            log.debug("BatchProcessing: Thread {} processing batch of {} details for pollId: {}", 
                     threadName, batch.size(), pollId);
            
            for (MarketPriceDetail detail : batch) {
                try {
                    marketPriceDataService.storeMarketPriceDetail(detail);
                } catch (Exception e) {
                    log.error("BatchProcessing: Thread {} failed to process detail for performanceId: {}, date: {} in pollId: {}",
                             threadName, detail.getPerformanceId(), detail.getDate(), pollId, e);
                }
            }
            
            log.debug("BatchProcessing: Thread {} completed batch of {} details for pollId: {}", 
                     threadName, batch.size(), pollId);
        }, batchProcessingTaskExecutor);
    }

    @Async("marketPriceTaskExecutor")
    public CompletableFuture<Void> processSingleMessageAsync(MarketPriceEnrichedEvent message, 
                                                            String pollId, String redisKeyPrefix) {
        return CompletableFuture.runAsync(() -> {
            try {
                marketPriceDataService.setRedisKey(redisKeyPrefix);
                
                if (message.getMarketPriceDataList() == null || message.getMarketPriceDataList().isEmpty()) {
                    log.debug("AsyncProcessing: No market price data found in single message for pollId: {}", pollId);
                    return;
                }

                for (MarketPriceDetail detail : message.getMarketPriceDataList()) {
                    try {
                        marketPriceDataService.storeMarketPriceDetail(detail);
                    } catch (Exception e) {
                        log.error("AsyncProcessing: Failed to process single detail for performanceId: {}, date: {} in pollId: {}",
                                 detail.getPerformanceId(), detail.getDate(), pollId, e);
                    }
                }
                
            } catch (Exception e) {
                log.error("AsyncProcessing: Error in single message async processing for pollId: {}", pollId, e);
                throw new RuntimeException("Single message async processing failed", e);
            }
        }, batchProcessingTaskExecutor);
    }

    private static <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }
}

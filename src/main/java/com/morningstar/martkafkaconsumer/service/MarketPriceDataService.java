package com.morningstar.martkafkaconsumer.service;

import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.data.domain.proto.TsDataProtoBuf;
import com.morningstar.martkafkaconsumer.config.MarketPriceDataConfig;
import com.morningstar.martkafkaconsumer.repository.RedisTsRepo;
import com.morningstar.martkafkaconsumer.util.Lz4Util;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;


@Slf4j
@Service
public class MarketPriceDataService {

    private final RedisTsRepo redisTsRepo;
    private final MarketPriceDataConfig config;
    @Getter
    @Setter
    private String redisKey = "";

    @Autowired
    public MarketPriceDataService(RedisTsRepo redisTsRepo, MarketPriceDataConfig config) {
        this.redisTsRepo = redisTsRepo;
        this.config = config;
        this.redisKey = config.getRedisKeyPrefix();
    }

    public void storeMarketPriceDetail(MarketPriceDetail marketPriceDetail) {
        // Input validation
        if (marketPriceDetail == null) {
            log.warn("MarketPriceDataService: MarketPriceDetail is null, skipping storage");
            return;
        }

        String investmentId = marketPriceDetail.getPerformanceId();
        LocalDate date = marketPriceDetail.getDate();

        // Validate required fields
        if (investmentId == null || date == null) {
            log.warn("MarketPriceDataService: Missing required fields - investmentId: {}, date: {}, skipping storage",
                    investmentId, date);
            return;
        }

        log.info("MarketPriceDataService: Storing market price data for investmentId: {}, date: {}",
                investmentId, date);

        try {
            // Store each data point using configured mappings
            Map<String, String> dataPointMapping = config.getDataPointMapping();

            // Get copyOverReason for use in mergeData
            CopyOverReasonEnum copyOverReason = marketPriceDetail.getCopyOverReason();

            Map<String, Object> dataPoints = Map.of(
                dataPointMapping.get("high"), getDoubleValue(marketPriceDetail.getHigh()),
                dataPointMapping.get("low"), getDoubleValue(marketPriceDetail.getLow()),
                dataPointMapping.get("open"), getDoubleValue(marketPriceDetail.getOpen()),
                dataPointMapping.get("close"), getDoubleValue(marketPriceDetail.getClose()),
                dataPointMapping.get("tradedVolume"), getDoubleValue(marketPriceDetail.getTradedVolume())
            );

            storeDataPointsBatch(investmentId, dataPoints, date, copyOverReason);

        } catch (Exception e) {
            log.error("MarketPriceDataService: Failed to store market price data for investmentId: {}, date: {}",
                    investmentId, date, e);
            throw new RuntimeException("Failed to store market price data", e);
        }
    }


    /**
     * Batch store multiple data points to Redis
     */
    private void storeDataPointsBatch(String investmentId, Map<String, Object> dataPoints,
                                     LocalDate date, CopyOverReasonEnum copyOverReason) {
        try {
            // 1. Calculate Redis key and field using configuration
            int year = date.getYear();
            int keyYear = (year / config.getYearsPerKey()) * config.getYearsPerKey(); // 10-year cycle start year
            int fieldStartYear = (year / config.getYearsPerField()) * config.getYearsPerField(); // 2-year cycle start year

            String redisKey = this.getRedisKey() + investmentId + ":" + keyYear;
            byte[] keyBytes = redisKey.getBytes(StandardCharsets.UTF_8);

            // 2. Prepare field bytes mapping for batch read
            Map<String, byte[]> fieldBytesMap = new HashMap<>();
            for (String dpId : dataPoints.keySet()) {
                String field = dpId + ":" + fieldStartYear;
                byte[] fieldBytes = field.getBytes(StandardCharsets.UTF_8);
                fieldBytesMap.put(dpId, fieldBytes);
            }

            // 3. Batch read existing data from Redis (improved performance)
            Map<String, TsDataProtoBuf.TSDataDouble> existingDataMap =
                getMultipleExistingData(keyBytes, fieldBytesMap, investmentId);

            // 4. Process each data point with existing data
            Map<byte[], byte[]> batchData = new HashMap<>();
            for (Map.Entry<String, Object> entry : dataPoints.entrySet()) {
                String dpId = entry.getKey();
                double value = (Double) entry.getValue();
                byte[] fieldBytes = fieldBytesMap.get(dpId);

                // Get existing data from batch read results
                TsDataProtoBuf.TSDataDouble existingData = existingDataMap.get(dpId);

                // 5. Merge new data
                TsDataProtoBuf.TSDataDouble updatedData = mergeData(existingData, investmentId, dpId, value, date, copyOverReason);

                // 6. Compress and store
                byte[] serializedData = updatedData.toByteArray();
                byte[] compressedData = Lz4Util.compress(serializedData);

                batchData.put(fieldBytes, compressedData);
            }

            // 7. Batch store to Redis without expiration
            setHashValueBatch(keyBytes, batchData);

            log.info("MarketPriceDataService: Batch stored {} data points for key: {}",
                    batchData.size(), redisKey);

        } catch (Exception e) {
            log.error("MarketPriceDataService: Error in batch storing data points for investmentId: {}, date: {}",
                    investmentId, date, e);
            throw new RuntimeException("Failed to batch store time series data", e);
        }
    }

    /**
     * Get existing data from Redis
     */
    private TsDataProtoBuf.TSDataDouble getExistingData(byte[] keyBytes, byte[] fieldBytes,
                                                        String investmentId, String dpId) {

        return redisTsRepo.getHashValue(keyBytes, Collections.singletonList(fieldBytes))
                .next()
                .flatMap(compressedData -> {
                    try {
                        byte[] decompressed = Lz4Util.decompress(compressedData);
                        return Mono.just(TsDataProtoBuf.TSDataDouble.parseFrom(decompressed));
                    } catch (Exception e) {
                        log.debug("MarketPriceDataService: Failed to decompress existing data, creating new", e);
                        return Mono.empty();
                    }
                })
                .blockOptional()
                .orElse(createEmptyTSData(investmentId, dpId));

    }

    /**
     * Get multiple existing data from Redis in batch
     * @param keyBytes Redis key bytes
     * @param fieldBytesMap Map of dpId to field bytes
     * @param investmentId Investment ID
     * @return Map of dpId to existing TSDataDouble
     */
    private Map<String, TsDataProtoBuf.TSDataDouble> getMultipleExistingData(byte[] keyBytes,
                                                                             Map<String, byte[]> fieldBytesMap,
                                                                             String investmentId) {

        List<byte[]> fieldBytesList = new ArrayList<>(fieldBytesMap.values());
        Map<byte[], byte[]> redisResults = redisTsRepo.getMultipleHashValues(keyBytes, fieldBytesList);

        Map<String, TsDataProtoBuf.TSDataDouble> results = new HashMap<>();

        for (Map.Entry<String, byte[]> entry : fieldBytesMap.entrySet()) {
            String dpId = entry.getKey();
            byte[] fieldBytes = entry.getValue();

            byte[] compressedData = redisResults.get(fieldBytes);
            TsDataProtoBuf.TSDataDouble existingData;

            if (compressedData != null && compressedData.length > 0) {
                try {
                    byte[] decompressed = Lz4Util.decompress(compressedData);
                    existingData = TsDataProtoBuf.TSDataDouble.parseFrom(decompressed);
                } catch (Exception e) {
                    log.error("MarketPriceDataService: Failed to decompress existing data for dpId: {}, creating new", dpId, e);
                    throw new RuntimeException("Failed to decompress existing data", e);
                }
            } else {
                existingData = createEmptyTSData(investmentId, dpId);
            }

            results.put(dpId, existingData);
        }

        return results;
    }

    /**
     * Create empty TSDataDouble object
     */
    private TsDataProtoBuf.TSDataDouble createEmptyTSData(String investmentId, String dpId) {
        return TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId(investmentId)
                .setDpId(dpId)
                .build();
    }

    /**
     * Merge new data with existing data
     */
    private TsDataProtoBuf.TSDataDouble mergeData(TsDataProtoBuf.TSDataDouble existingData,
                                                  String investmentId, String dpId,
                                                  double value, LocalDate date, CopyOverReasonEnum copyOverReason) {
        long epochDay = date.toEpochDay();

        List<Long> dates = new ArrayList<>(existingData.getDatesList());
        List<Double> values = new ArrayList<>(existingData.getValuesList());
        List<Long> copyOverDates = new ArrayList<>(existingData.getCopyOverDatesList());
        List<Integer> copyOverReasons = new ArrayList<>(existingData.getCopyOverReasonsList());

        int insertIndex = findInsertPosition(dates, epochDay);

        if (insertIndex < dates.size() && dates.get(insertIndex).equals(epochDay)) {
            values.set(insertIndex, value);

            if (copyOverReason != CopyOverReasonEnum.PRICE) {

                int copyOverIndex = copyOverDates.indexOf(epochDay);
                if (copyOverIndex >= 0) {
                    copyOverReasons.set(copyOverIndex, getCopyOverReasonValue(copyOverReason));
                } else {
                    copyOverDates.add(epochDay);
                    copyOverReasons.add(getCopyOverReasonValue(copyOverReason));
                }
            }
        } else {
            dates.add(insertIndex, epochDay);
            values.add(insertIndex, value);

            if (copyOverReason != CopyOverReasonEnum.PRICE) {
                copyOverDates.add(epochDay);
                copyOverReasons.add(getCopyOverReasonValue(copyOverReason));
            }
        }

        TsDataProtoBuf.TSDataDouble.Builder builder = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId(investmentId)
                .setDpId(dpId)
                .addAllDates(dates)
                .addAllValues(values)
                .addAllCopyOverDates(copyOverDates)
                .addAllCopyOverReasons(copyOverReasons);

        return builder.build();
    }

    private int findInsertPosition(List<Long> dates, long targetDate) {
        int left = 0;
        int right = dates.size();

        while (left < right) {
            int mid = left + (right - left) / 2;
            if (dates.get(mid) < targetDate) {
                left = mid + 1;
            } else {
                right = mid;
            }
        }

        return left;
    }

    /**
     * Set hash values in Redis without expiration
     */
    private void setHashValueBatch(byte[] keyBytes, Map<byte[], byte[]> batchData) {
        redisTsRepo.setHashWithoutExpiration(keyBytes, batchData)
                .blockFirst();
    }

    /**
     * Convert BigDecimal to double value
     */
    private double getDoubleValue(BigDecimal bigDecimal) {
        //log if bigDecimal is null
        if (bigDecimal == null) {
            log.debug("MarketPriceDataService: BigDecimal is null, returning 0.0");
            return 0.0;
        }
        return bigDecimal.doubleValue();
    }

    /**
     * Convert CopyOverReasonEnum to numeric value
     */
    private int getCopyOverReasonValue(CopyOverReasonEnum copyOverReason) {
        if (copyOverReason == null) {
            return -1;
        }

        // Convert enum to ordinal value
        return copyOverReason.ordinal();
    }
}

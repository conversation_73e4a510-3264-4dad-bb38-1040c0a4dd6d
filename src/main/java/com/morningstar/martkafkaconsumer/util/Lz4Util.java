package com.morningstar.martkafkaconsumer.util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import lombok.extern.slf4j.Slf4j;
import net.jpountz.lz4.LZ4Compressor;
import net.jpountz.lz4.LZ4Factory;
import net.jpountz.lz4.LZ4FastDecompressor;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class Lz4Util {
    private static final LZ4Factory factory = LZ4Factory.fastestInstance();
    private static final LZ4Compressor compressor = factory.highCompressor();
    private static final LZ4FastDecompressor decompressor = factory.fastDecompressor();
    private static final String EMPTY_STRING = "";

    private Lz4Util() {
    }

    public static String compress(String value) {
        return process(value, true);
    }

    public static String decompress(String value) {
        return process(value, false);
    }

    private static String process(String value, boolean compress) {
        if (StringUtils.isEmpty(value)) {
            return EMPTY_STRING;
        }
        String str;
        if (compress) {
            str = getString(compress(value.getBytes(StandardCharsets.UTF_8)));
        } else {
            str = new String(decompress(getBytes(value)));
        }

        return str;
    }

    public static byte[] compress(byte[] value) {
        int len = compressor.maxCompressedLength(value.length);
        byte[] temp = new byte[len];
        int compressedSize = compressor.compress(value, 0, value.length, temp, 0);
        byte[] out = new byte[compressedSize + Integer.BYTES];
        out[0] = (byte) (value.length >>> 24);
        out[1] = (byte) (value.length >>> 16);
        out[2] = (byte) (value.length >>> 8);
        out[3] = (byte) (value.length);
        System.arraycopy(temp, 0, out, Integer.BYTES, compressedSize);
        return out;
    }

    public static byte[] decompress(byte[] value) {
        try {
            int decompressedLen = ((value[0] & 0xFF) << 24) |
                    ((value[1] & 0xFF) << 16) |
                    ((value[2] & 0xFF) << 8) |
                    value[3] & 0xFF;
            byte[] out = new byte[decompressedLen];
            decompressor.decompress(value, Integer.BYTES, out, 0, out.length);
            return out;
        } catch (Exception ex) {
            log.info("Lz4Util decompress exception.");
            return value;
        }
    }

    private static byte[] getBytes(String bytes)
    {
        return Base64.getDecoder().decode(bytes);
    }

    private static String getString(byte[] bytes)
    {
        return new String(Base64.getEncoder().encode(bytes));
    }
}
package com.morningstar.martkafkaconsumer.util;

import io.lettuce.core.ReadFrom;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.resource.ClientResources;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RedisUtil {
    // Private constructor to prevent instantiation of utility class
    private RedisUtil() {
        throw new UnsupportedOperationException("RedisUtil: This is a utility class and cannot be instantiated");
    }

    // Constants for time calculations (more efficient than creating Duration objects)
    private static final long MILLIS_PER_SECOND = 1000L;
    private static final long MILLIS_PER_MINUTE = 60_000L; // 60 * 1000
    private static final long MILLIS_PER_HOUR = 3_600_000L; // 60 * 60 * 1000
    private static final long BUFFER_MILLIS = 10_000L; // 10 seconds buffer

    private static final byte[] EXPIRE_FIELD_KEY = "expire".getBytes(StandardCharsets.UTF_8);

    /**
     * Enum to specify time units for expiration
     */
    public enum TimeUnit {
        SECONDS(MILLIS_PER_SECOND),
        MINUTES(MILLIS_PER_MINUTE),
        HOURS(MILLIS_PER_HOUR);

        private final long multiplier;

        TimeUnit(long multiplier) {
            this.multiplier = multiplier;
        }

        public long toMillis(long value) {
            return value * multiplier;
        }
    }

    /**
     * Create multiple Lettuce connection factories with shared ClientResources for read-write operations
     *
     * @param host           Redis cluster host
     * @param port           Redis cluster port
     * @param clientName     Base name for Redis clients
     * @param totalFactories Number of connection factories to create
     * @return List of LettuceConnectionFactory instances
     */
    public static List<LettuceConnectionFactory> createLettuceConnectionFactories(
            String host, int port, String clientName, int totalFactories, ClientResources clientResources, ClusterClientOptions clientOptions) {
        return createLettuceConnectionFactoriesInternal(host, port, clientName, totalFactories, clientResources, clientOptions, false);
    }

    /**
     * Create multiple Lettuce connection factories with shared ClientResources for read-only operations
     *
     * @param host           Redis cluster host
     * @param port           Redis cluster port
     * @param clientName     Base name for Redis clients
     * @param totalFactories Number of connection factories to create
     * @return List of LettuceConnectionFactory instances configured for read-only operations
     */
    public static List<LettuceConnectionFactory> createLettuceConnectionFactoriesReadOnly(
            String host, int port, String clientName, int totalFactories, ClientResources clientResources, ClusterClientOptions clientOptions) {
        return createLettuceConnectionFactoriesInternal(host, port, clientName, totalFactories, clientResources, clientOptions, true);
    }

    /**
     * Internal helper method to create multiple Lettuce connection factories
     *
     * @param host           Redis cluster host
     * @param port           Redis cluster port
     * @param clientName     Base name for Redis clients
     * @param totalFactories Number of connection factories to create
     * @param clientResources Shared ClientResources instance
     * @param clientOptions   Shared ClientOptions instance
     * @param isReadOnly      Whether connections should prefer replica reads
     * @return List of LettuceConnectionFactory instances
     */
    private static List<LettuceConnectionFactory> createLettuceConnectionFactoriesInternal(
            String host, int port, String clientName, int totalFactories,
            ClientResources clientResources, ClusterClientOptions clientOptions, boolean isReadOnly) {

        List<LettuceConnectionFactory> factories = new ArrayList<>();

        for (int i = 0; i < totalFactories; i++) {
            String suffix = isReadOnly ? "-readonly-" : "-";
            String fullClientName = clientName + suffix + i;
            LettuceConnectionFactory factory = createLettuceConnectionFactory(host, port, fullClientName, isReadOnly, clientResources, clientOptions);
            factories.add(factory);
        }

        return factories;
    }

    /**
     * Create a single Lettuce connection factory with provided ClientResources and ClientOptions
     *
     * @param host            Redis cluster host
     * @param port            Redis cluster port
     * @param clientName      Client name for the Redis connection
     * @param isReadOnly      Whether this connection should prefer replica reads
     * @param clientResources Shared ClientResources instance
     * @param clientOptions   Shared ClientOptions instance
     * @return LettuceConnectionFactory instance
     */
    public static LettuceConnectionFactory createLettuceConnectionFactory(
            String host, int port, String clientName, boolean isReadOnly,
            ClientResources clientResources, ClusterClientOptions clientOptions) {

        // Configure Redis cluster
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        clusterConfig.clusterNode(host, port);

        // Build client configuration with appropriate read preference
        LettuceClientConfiguration.LettuceClientConfigurationBuilder configBuilder = LettuceClientConfiguration.builder()
                .clientOptions(clientOptions)
                .clientResources(clientResources)
                .clientName(clientName);

        // Set read preference based on isReadOnly flag
        if (isReadOnly) {
            configBuilder.readFrom(ReadFrom.REPLICA_PREFERRED);
        }
        // Note: No need to set ReadFrom.MASTER as it's the default behavior

        LettuceClientConfiguration lettuceClientConfiguration = configBuilder.build();

        // Create and initialize the connection factory
        LettuceConnectionFactory factory = new LettuceConnectionFactory(clusterConfig, lettuceClientConfiguration);
        factory.afterPropertiesSet();

        return factory;
    }

    /**
     * Add expire field to map with specified time unit
     *
     * @param fieldMap the map to add the expire field to
     * @param expire the expiration time value
     * @param unit the time unit (SECONDS, MINUTES, or HOURS)
     */
    public static void addExpireField(Map<byte[], byte[]> fieldMap, long expire, TimeUnit unit) {
        long expireTimeWithBuffer = System.currentTimeMillis() + unit.toMillis(expire) + BUFFER_MILLIS;
        fieldMap.put(EXPIRE_FIELD_KEY, NumberUtil.longToBytes(expireTimeWithBuffer));
    }

    public static byte[] getExpireKey() {
        return EXPIRE_FIELD_KEY;
    }
}

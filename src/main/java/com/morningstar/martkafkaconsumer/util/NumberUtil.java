package com.morningstar.martkafkaconsumer.util;

import java.nio.ByteBuffer;

public class NumberUtil {
    private NumberUtil() {
        throw new UnsupportedOperationException("NumberUtil: This is a utility class and cannot be instantiated");
    }
    /**
     * Convert byte array to long using bit shifting for better performance
     * Optimized for single value conversion - faster than ByteBuffer
     */
    public static long bytesToLong(byte[] bytes) {
        if (bytes == null || bytes.length != 8) {
            throw new IllegalArgumentException("NumberUtil bytesToLong: Byte array must be exactly 8 bytes");
        }
        return convertBytesToLong(bytes);
    }

    /**
     * Convert byte array to long with default value, using bit shifting for performance
     */
    public static long bytesToLong(byte[] bytes, long defaultValue) {
        if (bytes == null || bytes.length != 8) {
            return defaultValue;
        }
        return convertBytesToLong(bytes);
    }

    /**
     * Private helper method to perform the actual bit shifting conversion
     * Eliminates code duplication between the two bytesToLong methods
     */
    private static long convertBytesToLong(byte[] bytes) {
        return ((long) bytes[0] & 0xff) << 56 |
               ((long) bytes[1] & 0xff) << 48 |
               ((long) bytes[2] & 0xff) << 40 |
               ((long) bytes[3] & 0xff) << 32 |
               ((long) bytes[4] & 0xff) << 24 |
               ((long) bytes[5] & 0xff) << 16 |
               ((long) bytes[6] & 0xff) << 8  |
               ((long) bytes[7] & 0xff);
    }

    /**
     * Convert a single long value to byte array - optimized for single value conversion
     * Uses bit shifting for better performance than ByteBuffer for single values
     */
    public static byte[] longToBytes(long value) {
        return new byte[] {
            (byte) (value >>> 56),
            (byte) (value >>> 48),
            (byte) (value >>> 40),
            (byte) (value >>> 32),
            (byte) (value >>> 24),
            (byte) (value >>> 16),
            (byte) (value >>> 8),
            (byte) value
        };
    }

    /**
     * Convert multiple long values to a single byte array - optimized for batch operations
     * Uses ByteBuffer for better performance when converting multiple values
     */
    public static byte[] longsToBytes(long... longs) {
        if (longs.length == 1) {
            // For single value, use bit shifting
            return longToBytes(longs[0]);
        }

        // For multiple values, use ByteBuffer
        ByteBuffer buffer = ByteBuffer.allocate(longs.length * Long.BYTES);
        for (long l : longs) {
            buffer.putLong(l);
        }
        return buffer.array();
    }

    /**
     * Convert byte array to double using direct bit shifting for better performance
     * Optimized for single value conversion - faster than long conversion
     */
    public static double bytesToDouble(byte[] bytes) {
        if (bytes == null || bytes.length != 8) {
            throw new IllegalArgumentException("NumberUtil bytesToDouble: Byte array must be exactly 8 bytes");
        }
        return convertBytesToDouble(bytes);
    }

    /**
     * Convert byte array to double with default value, using direct bit shifting for performance
     */
    public static double bytesToDouble(byte[] bytes, double defaultValue) {
        if (bytes == null || bytes.length != 8) {
            return defaultValue;
        }
        return convertBytesToDouble(bytes);
    }

    /**
     * Private helper method to perform direct bytes to double conversion
     * More direct approach - still uses long intermediate for bit operations
     * but makes the intention clearer
     */
    private static double convertBytesToDouble(byte[] bytes) {
        return Double.longBitsToDouble(
            ((long) bytes[0] & 0xff) << 56 |
            ((long) bytes[1] & 0xff) << 48 |
            ((long) bytes[2] & 0xff) << 40 |
            ((long) bytes[3] & 0xff) << 32 |
            ((long) bytes[4] & 0xff) << 24 |
            ((long) bytes[5] & 0xff) << 16 |
            ((long) bytes[6] & 0xff) << 8  |
            ((long) bytes[7] & 0xff)
        );
    }

    /**
     * Convert a single double value to byte array - optimized for single value conversion
     * Uses direct bit shifting for better performance than ByteBuffer for single values
     */
    public static byte[] doubleToBytes(double value) {
        return convertDoubleToBytes(value);
    }

    /**
     * Private helper method to perform direct double to bytes conversion
     * Uses bit shifting without intermediate method calls for better performance
     */
    private static byte[] convertDoubleToBytes(double value) {
        long longBits = Double.doubleToRawLongBits(value);
        return new byte[] {
            (byte) (longBits >>> 56),
            (byte) (longBits >>> 48),
            (byte) (longBits >>> 40),
            (byte) (longBits >>> 32),
            (byte) (longBits >>> 24),
            (byte) (longBits >>> 16),
            (byte) (longBits >>> 8),
            (byte) longBits
        };
    }

    /**
     * Convert multiple double values to a single byte array - optimized for batch operations
     * Uses ByteBuffer for better performance when converting multiple values
     */
    public static byte[] doublesToBytes(double... doubles) {
        if (doubles.length == 1) {
            // For single value, use bit shifting
            return doubleToBytes(doubles[0]);
        }

        // For multiple values, use ByteBuffer
        ByteBuffer buffer = ByteBuffer.allocate(doubles.length * Double.BYTES);
        for (double d : doubles) {
            buffer.putDouble(d);
        }
        return buffer.array();
    }
}

syntax = "proto3";

package protobuf;

option java_package = "com.morningstar.data.domain.proto";
option java_outer_classname = "TsDataProtoBuf";

message TSDataDouble {
  string investmentId = 1;
  string dpId = 2;
  repeated int64 dates = 3 [packed = true];
  repeated double values = 4 [packed = true];
  repeated int64 copyOverDates = 5 [packed = true];
  repeated int32 copyOverReasons = 6 [packed = true];
}
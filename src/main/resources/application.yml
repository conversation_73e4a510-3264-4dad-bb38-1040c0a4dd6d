kafka:
  region: aws-us-east-1
  environment: devcloud
  clientPrincipal: svc-your-service-name
  keytabRef: /path/to/your/keytab

  consumer-endpoints:
    market_price_list_updated:
      topic: market_price_list_updated
      clientId: DPDA_Market_Data_Service
      groupId: dpda_market_price_group
      keyDeserializer: org.apache.kafka.common.serialization.LongDeserializer
      valueDeserializer: com.morningstar.dp.messaging.common.serialization.avro.AvroWithSchemaGenericSerde
      serdePojoClass: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent
    market_price_enriched_event:
      topic: market_price_enriched_event
      clientId: DPDA_Market_Data_Service
      groupId: dpda_market_price_group
      keyDeserializer: org.apache.kafka.common.serialization.LongDeserializer
      valueDeserializer: com.morningstar.dp.messaging.common.serialization.avro.AvroWithSchemaGenericSerde
      serdePojoClass: com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent

redis:
  ts-data:
    host: ts-data-v8-cluster-stg.bg4uxb.clustercfg.use1.cache.amazonaws.com
    port: 6379
  client-name:

market-price:
  redis-key-prefix: "ts:MarketPrice:"
  years-per-key: 20
  years-per-field: 10
  data-point-mapping:
    high: "EO001"
    low: "EO002"
    open: "EO003"
    close: "EO004"
    tradedVolume: "EO005"
    copyOverReason: "EO006"

spring:
  application:
    name: mart-kafka-consumer
  profiles:
    active: dev

logging:
  level:
    com: INFO
    org: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
---
# dev env
spring:
  config:
    activate:
      on-profile: dev
kafka:
  environment: devcloud
  consumer-endpoints:
    market_price_enriched_event:
      topic: 04_13_12_MyNewWorkflowName4
      clientId: DPDA_Market_Data_Service
      groupId: dpda_market_price_group1

logging:
  level:
    root: DEBUG


---
# prod env
spring:
  config:
    activate:
      on-profile: prod

kafka:
  environment: prod

logging:
  level:
    root: WARN

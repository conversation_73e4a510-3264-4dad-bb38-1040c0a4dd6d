package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MarketPriceConsumerPerformanceTest {

    @Mock
    private KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> kafkaConsumerEndpoint;

    @Mock
    private MarketPriceDataService marketPriceDataService;

    private ExecutorService executorService;
    private MarketPriceEnrichedConsumer enrichedConsumer;
    private MarketPriceRawConsumer rawConsumer;

    @BeforeEach
    void setUp() {
        executorService = Executors.newFixedThreadPool(4);
        
        enrichedConsumer = new MarketPriceEnrichedConsumer(
            kafkaConsumerEndpoint, marketPriceDataService, executorService);
        
        rawConsumer = new MarketPriceRawConsumer(
            kafkaConsumerEndpoint, marketPriceDataService, executorService);

        doNothing().when(marketPriceDataService).setRedisKey(anyString());
        doNothing().when(marketPriceDataService).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void testEnrichedConsumerPerformanceWithMultipleMessages() {
        List<MarketPriceEnrichedEvent> messages = createTestMessages(100);
        
        long startTime = System.currentTimeMillis();
        enrichedConsumer.processMessages(messages, "test-poll-id");
        long endTime = System.currentTimeMillis();
        
        System.out.println("EnrichedConsumer processed 100 messages in " + (endTime - startTime) + "ms");
        
        verify(marketPriceDataService, times(1)).setRedisKey("ts:MarketPrice:");
        verify(marketPriceDataService, times(200)).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void testRawConsumerPerformanceWithMultipleMessages() {
        List<MarketPriceEnrichedEvent> messages = createTestMessages(100);
        
        long startTime = System.currentTimeMillis();
        rawConsumer.processMessages(messages, "test-poll-id");
        long endTime = System.currentTimeMillis();
        
        System.out.println("RawConsumer processed 100 messages in " + (endTime - startTime) + "ms");
        
        verify(marketPriceDataService, times(1)).setRedisKey("ts:RawMarketPrice:");
        verify(marketPriceDataService, times(200)).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void testEnrichedConsumerWithEmptyMessages() {
        List<MarketPriceEnrichedEvent> emptyMessages = Arrays.asList(
            createEmptyEvent(), createEmptyEvent()
        );
        
        enrichedConsumer.processMessages(emptyMessages, "test-poll-id");
        
        verify(marketPriceDataService, times(1)).setRedisKey("ts:MarketPrice:");
        verify(marketPriceDataService, never()).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    private List<MarketPriceEnrichedEvent> createTestMessages(int count) {
        List<MarketPriceEnrichedEvent> messages = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            MarketPriceEnrichedEvent event = new MarketPriceEnrichedEvent();
            event.setMarketPriceDataList(Arrays.asList(
                createTestMarketPriceDetail("PERF" + (i * 2 + 1)),
                createTestMarketPriceDetail("PERF" + (i * 2 + 2))
            ));
            messages.add(event);
        }
        
        return messages;
    }

    private MarketPriceEnrichedEvent createEmptyEvent() {
        MarketPriceEnrichedEvent event = new MarketPriceEnrichedEvent();
        event.setMarketPriceDataList(new ArrayList<>());
        return event;
    }

    private MarketPriceDetail createTestMarketPriceDetail(String performanceId) {
        MarketPriceDetail detail = new MarketPriceDetail();
        detail.setPerformanceId(performanceId);
        detail.setHigh(BigDecimal.valueOf(105.50));
        detail.setLow(BigDecimal.valueOf(98.25));
        detail.setOpen(BigDecimal.valueOf(100.00));
        detail.setClose(BigDecimal.valueOf(103.75));
        detail.setTradedVolume(BigDecimal.valueOf(1500000));
        detail.setDate(LocalDate.of(2023, 6, 15));
        detail.setCopyOverReason(CopyOverReasonEnum.PRICE);
        return detail;
    }
}

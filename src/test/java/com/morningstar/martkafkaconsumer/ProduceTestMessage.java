package com.morningstar.martkafkaconsumer;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.eod.events.MarketPriceTypeEnum;
import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.dp.messaging.common.IKafkaFacade;
import com.morningstar.dp.messaging.common.IKafkaFactory;
import com.morningstar.dp.messaging.common.impl.KafkaFacade;
import com.morningstar.dp.messaging.common.util.ClientType;
import com.morningstar.dp.messaging.common.util.ExtendedConfig;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Properties;
import java.util.concurrent.Future;

public class ProduceTestMessage {

    private final IKafkaFactory<Long, MarketPriceEnrichedEvent> kafkaFactoryDevcloud;
    private static final String KAFKA_USAGE_EXAMPLE_CONFIG_NUM_ACCOUNT_RECORDS_DEFAULT = "1";
    private static final String KAFKA_USAGE_EXAMPLE_WORKFLOW = "workflow";
    //04_13_12_MyNewWorkflowName market_price_enriched_event market_price_list_updated
    private static final String DEFAULT_WORKFLOW_NAME = "04_13_12_MyNewWorkflowName4";


    /**
     *
     * @param kafkaFacade
     */
    public ProduceTestMessage(IKafkaFacade<Long, MarketPriceEnrichedEvent> kafkaFacade) {
        this.kafkaFactoryDevcloud = kafkaFacade.getKafkaFactory();
    }


    /**
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        //needs to specify a brand new workflow that currently does not exist at all. Default it if not set in the command line
        //make sure your new workflow name does not clashes with the already configured know workflows in devcloud. It can be any string
        final String workflow = System.getProperty(KAFKA_USAGE_EXAMPLE_WORKFLOW, DEFAULT_WORKFLOW_NAME);

        //this is your new full path avro schema class - your payload
        final String serdePojoClass = MarketPriceEnrichedEvent.class.getName();

        int numAccountRecordsToSend = Integer.parseInt(KAFKA_USAGE_EXAMPLE_CONFIG_NUM_ACCOUNT_RECORDS_DEFAULT);


        //now use KafkaFacade to avoid using the two system properties
        KafkaFacade<Long, MarketPriceEnrichedEvent> kafkaFacadeDevcloud = new KafkaFacade<Long, MarketPriceEnrichedEvent>("aws-us-east-1", "devcloud");
        ProduceTestMessage producer = new ProduceTestMessage(kafkaFacadeDevcloud);
        option1(producer.kafkaFactoryDevcloud, workflow, numAccountRecordsToSend, serdePojoClass);
    }


    /**
     * Need jdk 1.7 or up
     * Option 1 - Highly recommended - the try-with-resources will take of closing the producer, therefore flushing any outstanding messages to the broker
     * @param kafkaFactory
     * @param workflow
     */
    private static void option1(IKafkaFactory<Long, MarketPriceEnrichedEvent> kafkaFactory, String workflow, int numAccountMsgToSend, final String serdePojoClass) {
        //The  first parameter in the overrides prop will trigger, if possbile, the runtime adhoc generation of your brand new workflow with your new specific topic (with the name of "devcloud_" +  <your workflow name>) and the specific avro class
        Properties overrides = new Properties() {{
            put(ExtendedConfig.SERDE_POJO_CLASS_NAME_TAG, serdePojoClass);
            put(ConsumerConfig.CLIENT_ID_CONFIG, ManagementFactory.getRuntimeMXBean().getName());
        }};

        System.out.println("This is to prove that now we have configured your brand new workflow/topic/avro schema- ConfigService: " + kafkaFactory.getMSConfig(workflow, ClientType.PRODUCER, overrides));

        String topic = kafkaFactory.getMSConfig(workflow, ClientType.PRODUCER, overrides).getProducerProps().getProperty(ExtendedConfig.TOPIC_NAME_TAG);
        try(Producer<Long,MarketPriceEnrichedEvent> producer = kafkaFactory.getProducer(workflow, overrides)) {
            for(int i = 0; i < numAccountMsgToSend; i++) {
                MarketPriceEnrichedEvent p = createMessageData(i);
                ProducerRecord<Long, MarketPriceEnrichedEvent> pr = new ProducerRecord<Long,MarketPriceEnrichedEvent>(topic, p );
                Future<RecordMetadata> future = producer.send(pr);
                System.out.println(String.format("------> Sending this record : %s", pr));
            }
        }
    }

    private static MarketPriceEnrichedEvent createMessageData(int i) {

        MarketPriceDetail marketPriceDetail = new MarketPriceDetail();
        marketPriceDetail.setPerformanceId("PERF_" + i);
        marketPriceDetail.setInvestmentType("Stock");
        marketPriceDetail.setHigh(BigDecimal.valueOf(100.0).setScale(17, BigDecimal.ROUND_HALF_UP));
        marketPriceDetail.setLow(BigDecimal.valueOf(90.0).setScale(17, BigDecimal.ROUND_HALF_UP));
        marketPriceDetail.setOpen(BigDecimal.valueOf(95.0).setScale(17, BigDecimal.ROUND_HALF_UP));
        marketPriceDetail.setClose(BigDecimal.valueOf(98.0).setScale(17, BigDecimal.ROUND_HALF_UP));
        marketPriceDetail.setTradedVolume(BigDecimal.valueOf(1000000.0).setScale(17, BigDecimal.ROUND_HALF_UP));
        marketPriceDetail.setDate(LocalDate.now());
        marketPriceDetail.setCurrencyCode("USD");
        marketPriceDetail.setCopyOverReason(CopyOverReasonEnum.PRICE);

        // Create the list and add the market price detail
        ArrayList<MarketPriceDetail> marketPriceDataList = new ArrayList<>();
        marketPriceDataList.add(marketPriceDetail);

        MarketPriceEnrichedEvent marketPriceEnrichedEvent = new MarketPriceEnrichedEvent();

        marketPriceEnrichedEvent.setMarketPriceDataList(marketPriceDataList);

        marketPriceEnrichedEvent.setEventTimestampUtc(java.time.Instant.now());

        return marketPriceEnrichedEvent;
    }
}

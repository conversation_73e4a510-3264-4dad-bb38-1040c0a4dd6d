package com.morningstar.martkafkaconsumer.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.nio.charset.StandardCharsets;
import java.util.Random;

/**
 * Unit tests for Lz4Util compression utility
 */
class Lz4UtilTest {

    @Test
    void testCompressAndDecompress_EmptyArray() {
        byte[] original = new byte[0];
        byte[] compressed = Lz4Util.compress(original);
        byte[] decompressed = Lz4Util.decompress(compressed);
        
        assertArrayEquals(original, decompressed);
    }

    @Test
    void testCompressAndDecompress_SmallData() {
        String testString = "Hello, World!";
        byte[] original = testString.getBytes(StandardCharsets.UTF_8);
        
        byte[] compressed = Lz4Util.compress(original);
        byte[] decompressed = Lz4Util.decompress(compressed);
        
        assertArrayEquals(original, decompressed);
        assertEquals(testString, new String(decompressed, StandardCharsets.UTF_8));
    }

    @Test
    void testCompressAndDecompress_LargeData() {
        // Create a large array with repeating pattern (should compress well)
        byte[] original = new byte[10000];
        for (int i = 0; i < original.length; i++) {
            original[i] = (byte) (i % 256);
        }
        
        byte[] compressed = Lz4Util.compress(original);
        byte[] decompressed = Lz4Util.decompress(compressed);
        
        assertArrayEquals(original, decompressed);
        // Compressed size should be smaller than original for this pattern
        assertTrue(compressed.length < original.length);
    }

    @Test
    void testCompressAndDecompress_RandomData() {
        Random random = new Random(12345); // Fixed seed for reproducible tests
        byte[] original = new byte[1000];
        random.nextBytes(original);
        
        byte[] compressed = Lz4Util.compress(original);
        byte[] decompressed = Lz4Util.decompress(compressed);
        
        assertArrayEquals(original, decompressed);
    }
}

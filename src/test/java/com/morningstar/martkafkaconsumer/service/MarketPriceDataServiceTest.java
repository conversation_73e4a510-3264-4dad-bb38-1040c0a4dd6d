package com.morningstar.martkafkaconsumer.service;

import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.martkafkaconsumer.config.MarketPriceDataConfig;
import com.morningstar.martkafkaconsumer.repository.RedisTsRepo;
import com.morningstar.martkafkaconsumer.service.AsyncMarketPriceProcessingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class MarketPriceDataServiceTest {

    @Mock
    private RedisTsRepo redisTsRepo;

    @Mock
    private MarketPriceDataConfig config;

    private MarketPriceDataService marketPriceDataService;

    @BeforeEach
    void setUp() {
        lenient().when(config.getRedisKeyPrefix()).thenReturn("ts:MarketPrice:");
        lenient().when(config.getYearsPerKey()).thenReturn(10);
        lenient().when(config.getYearsPerField()).thenReturn(2);
        lenient().when(config.getDefaultExpirationHours()).thenReturn(24L);
        lenient().when(config.getDataPointMapping()).thenReturn(Map.of(
            "high", "EO001",
            "low", "EO002",
            "open", "EO003",
            "close", "EO004",
            "tradedVolume", "EO005",
            "copyOverReason", "EO006"
        ));

        lenient().when(redisTsRepo.getHashValue(any(byte[].class), anyList())).thenReturn(Flux.empty());
        lenient().when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(new HashMap<>());
        lenient().when(redisTsRepo.setHashWithoutExpiration(any(byte[].class), anyMap())).thenReturn(Flux.just(true));

        marketPriceDataService = new MarketPriceDataService(redisTsRepo, config);
    }

    @Test
    void testStoreMarketPriceDetail_ValidData() {
        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();

        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });

        verify(redisTsRepo, times(1)).getMultipleHashValues(any(byte[].class), anyList());
        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testStoreMarketPriceDetail_NullInput() {
        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(null);
        });

        verify(redisTsRepo, never()).getMultipleHashValues(any(byte[].class), anyList());
        verify(redisTsRepo, never()).setHash(any(byte[].class), anyMap(), anyLong());
    }

    @Test
    void testStoreMarketPriceDetail_MissingInvestmentId() {
        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();
        marketPriceDetail.setPerformanceId(null);

        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });

        verify(redisTsRepo, never()).getMultipleHashValues(any(byte[].class), anyList());
        verify(redisTsRepo, never()).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testStoreMarketPriceDetail_MissingDate() {
        // Arrange
        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();
        marketPriceDetail.setDate(null);

        // Act & Assert
        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });

        // Verify no Redis operations were called
        verify(redisTsRepo, never()).getHashValue(any(byte[].class), anyList());
        verify(redisTsRepo, never()).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testRedisKeyGeneration() {
        // Test key generation for different years
        LocalDate date2020 = LocalDate.of(2020, 5, 15);
        LocalDate date2025 = LocalDate.of(2025, 8, 20);
        LocalDate date1975 = LocalDate.of(1975, 12, 31);

        MarketPriceDetail detail2020 = createTestMarketPriceDetail();
        detail2020.setDate(date2020);
        detail2020.setPerformanceId("TEST001");

        MarketPriceDetail detail2025 = createTestMarketPriceDetail();
        detail2025.setDate(date2025);
        detail2025.setPerformanceId("TEST001");

        MarketPriceDetail detail1975 = createTestMarketPriceDetail();
        detail1975.setDate(date1975);
        detail1975.setPerformanceId("TEST001");

        // Store all details
        marketPriceDataService.storeMarketPriceDetail(detail2020);
        marketPriceDataService.storeMarketPriceDetail(detail2025);
        marketPriceDataService.storeMarketPriceDetail(detail1975);

        // Verify Redis operations were called for each detail (1 batch call per detail * 3 details = 3)
        verify(redisTsRepo, times(3)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testCopyOverReasonHandling() {
        // Test different CopyOverReason values
        MarketPriceDetail detail1 = createTestMarketPriceDetail();
        detail1.setCopyOverReason(CopyOverReasonEnum.PRICE);

        MarketPriceDetail detail2 = createTestMarketPriceDetail();
        detail2.setCopyOverReason(null);

        // Should not throw exceptions
        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(detail1);
            marketPriceDataService.storeMarketPriceDetail(detail2);
        });

        verify(redisTsRepo, times(2)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testNullBigDecimalHandling() {
        // Test with null BigDecimal values
        MarketPriceDetail detail = createTestMarketPriceDetail();
        detail.setHigh(null);
        detail.setLow(null);
        detail.setOpen(null);
        detail.setClose(null);
        detail.setTradedVolume(null);

        // Should not throw exceptions
        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(detail);
        });

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());
    }

    @Test
    void testRedisOperationFailure() {
        // Arrange - Mock Redis to throw exception
        when(redisTsRepo.setHashWithoutExpiration(any(byte[].class), anyMap()))
            .thenReturn(Flux.error(new RuntimeException("Redis connection failed")));

        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });
    }

    @Test
    void testStoreMarketPriceDetail_BatchReadOptimization() {
        MarketPriceDetail marketPriceDetail = createTestMarketPriceDetail();

        Map<byte[], byte[]> mockExistingData = new HashMap<>();
        when(redisTsRepo.getMultipleHashValues(any(byte[].class), anyList())).thenReturn(mockExistingData);

        assertDoesNotThrow(() -> {
            marketPriceDataService.storeMarketPriceDetail(marketPriceDetail);
        });

        verify(redisTsRepo, times(1)).getMultipleHashValues(any(byte[].class), argThat(fields ->
            fields != null && fields.size() == 5));

        verify(redisTsRepo, times(1)).setHashWithoutExpiration(any(byte[].class), anyMap());

        verify(redisTsRepo, never()).getHashValue(any(byte[].class), anyList());
    }

    private MarketPriceDetail createTestMarketPriceDetail() {
        MarketPriceDetail detail = new MarketPriceDetail();
        detail.setPerformanceId("TEST_PERF_001");
        detail.setHigh(BigDecimal.valueOf(105.50));
        detail.setLow(BigDecimal.valueOf(98.25));
        detail.setOpen(BigDecimal.valueOf(100.00));
        detail.setClose(BigDecimal.valueOf(103.75));
        detail.setTradedVolume(BigDecimal.valueOf(1500000));
        detail.setDate(LocalDate.of(2023, 6, 15));
        detail.setCopyOverReason(CopyOverReasonEnum.PRICE);
        return detail;
    }
}

package com.morningstar.martkafkaconsumer.service;

import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AsyncMarketPriceProcessingServiceTest {

    @Mock
    private MarketPriceDataService marketPriceDataService;

    @Mock
    private Executor batchProcessingTaskExecutor;

    private AsyncMarketPriceProcessingService asyncProcessingService;

    @BeforeEach
    void setUp() {
        ThreadPoolTaskExecutor realExecutor = new ThreadPoolTaskExecutor();
        realExecutor.setCorePoolSize(2);
        realExecutor.setMaxPoolSize(4);
        realExecutor.setQueueCapacity(100);
        realExecutor.setThreadNamePrefix("Test-");
        realExecutor.initialize();

        asyncProcessingService = new AsyncMarketPriceProcessingService(marketPriceDataService, realExecutor);
    }

    @Test
    void testProcessMessagesAsync() {
        MarketPriceEnrichedEvent event = createTestEvent();
        List<MarketPriceEnrichedEvent> messages = Arrays.asList(event);

        doNothing().when(marketPriceDataService).setRedisKey(anyString());
        doNothing().when(marketPriceDataService).storeMarketPriceDetail(any(MarketPriceDetail.class));

        CompletableFuture<Void> result = asyncProcessingService.processMessagesAsync(
            messages, "test-poll-id", "ts:TestMarketPrice:");

        assertDoesNotThrow(() -> result.join());

        verify(marketPriceDataService, times(1)).setRedisKey("ts:TestMarketPrice:");
        verify(marketPriceDataService, times(2)).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void testProcessBatchAsync() {
        List<MarketPriceDetail> batch = Arrays.asList(
            createTestMarketPriceDetail("PERF001"),
            createTestMarketPriceDetail("PERF002")
        );

        doNothing().when(marketPriceDataService).storeMarketPriceDetail(any(MarketPriceDetail.class));

        CompletableFuture<Void> result = asyncProcessingService.processBatchAsync(batch, "test-poll-id");

        assertDoesNotThrow(() -> result.join());

        verify(marketPriceDataService, times(2)).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void testProcessSingleMessageAsync() {
        MarketPriceEnrichedEvent event = createTestEvent();

        doNothing().when(marketPriceDataService).setRedisKey(anyString());
        doNothing().when(marketPriceDataService).storeMarketPriceDetail(any(MarketPriceDetail.class));

        CompletableFuture<Void> result = asyncProcessingService.processSingleMessageAsync(
            event, "test-poll-id", "ts:TestMarketPrice:");

        assertDoesNotThrow(() -> result.join());

        verify(marketPriceDataService, times(1)).setRedisKey("ts:TestMarketPrice:");
        verify(marketPriceDataService, times(2)).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    @Test
    void testProcessMessagesAsyncWithEmptyMessage() {
        MarketPriceEnrichedEvent emptyEvent = new MarketPriceEnrichedEvent();
        emptyEvent.setMarketPriceDataList(Arrays.asList());
        List<MarketPriceEnrichedEvent> messages = Arrays.asList(emptyEvent);

        doNothing().when(marketPriceDataService).setRedisKey(anyString());

        CompletableFuture<Void> result = asyncProcessingService.processMessagesAsync(
            messages, "test-poll-id", "ts:TestMarketPrice:");

        assertDoesNotThrow(() -> result.join());

        verify(marketPriceDataService, times(1)).setRedisKey("ts:TestMarketPrice:");
        verify(marketPriceDataService, never()).storeMarketPriceDetail(any(MarketPriceDetail.class));
    }

    private MarketPriceEnrichedEvent createTestEvent() {
        MarketPriceEnrichedEvent event = new MarketPriceEnrichedEvent();
        event.setMarketPriceDataList(Arrays.asList(
            createTestMarketPriceDetail("PERF001"),
            createTestMarketPriceDetail("PERF002")
        ));
        return event;
    }

    private MarketPriceDetail createTestMarketPriceDetail(String performanceId) {
        MarketPriceDetail detail = new MarketPriceDetail();
        detail.setPerformanceId(performanceId);
        detail.setHigh(BigDecimal.valueOf(105.50));
        detail.setLow(BigDecimal.valueOf(98.25));
        detail.setOpen(BigDecimal.valueOf(100.00));
        detail.setClose(BigDecimal.valueOf(103.75));
        detail.setTradedVolume(BigDecimal.valueOf(1500000));
        detail.setDate(LocalDate.of(2023, 6, 15));
        detail.setCopyOverReason(CopyOverReasonEnum.PRICE);
        return detail;
    }
}
